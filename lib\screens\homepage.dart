import 'package:flutter/material.dart';

import '../theme/app_colors.dart';

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
          preferredSize: const Size.fromHeight(50),
          child: AppBar(
            title: const Text('Online Medicine'),
            actions: [
              // notification icon
              IconButton(
                onPressed: () {},
                icon: const Icon(Icons.notifications),
              ),
              // profile icon
              IconButton(
                onPressed: () {},
                icon: const Icon(Icons.person),
              ),
            ],
          )),
      // Add the drawer here
      drawer: _buildDrawer(context),
      body: const Center(
        child: Text('Main Content Area'),
      ),
    );
  }

  /// Builds the navigation drawer with modern design and proper theming
  ///
  /// The drawer includes:
  /// - A beautiful header with user information and gradient background
  /// - Navigation items with icons, hover effects, and proper spacing
  /// - Visual separators and logout functionality
  /// - Consistent theming using AppColors
  Widget _buildDrawer(BuildContext context) {
    return Drawer(
      // Set the drawer background color to match our theme
      backgroundColor: AppColors.surface,
      // Add subtle shadow for depth
      elevation: 8,
      // Custom shape with rounded corners on the right side
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topRight: Radius.circular(16),
          bottomRight: Radius.circular(16),
        ),
      ),
      child: Column(
        children: [
          // Header section with user info and gradient background
          _buildDrawerHeader(),

          // Expanded scrollable content area
          Expanded(
            child: ListView(
              padding: const EdgeInsets.symmetric(vertical: 8),
              children: [
                // Main navigation section
                _buildSectionHeader('Main Menu'),

                _buildDrawerItem(
                  icon: Icons.upload_file_outlined,
                  selectedIcon: Icons.upload_file,
                  title: 'Upload Prescription',
                  subtitle: 'Get medicines delivered',
                  onTap: () {
                    Navigator.pop(context);
                    // Navigate to upload prescription screen
                  },
                ),

                _buildDrawerItem(
                  icon: Icons.shopping_cart_outlined,
                  selectedIcon: Icons.shopping_cart,
                  title: 'Supplements',
                  subtitle: 'Health & wellness products',
                  onTap: () {
                    Navigator.pop(context);
                    // Navigate to supplements screen
                  },
                ),

                _buildDrawerItem(
                  icon: Icons.person_outline,
                  selectedIcon: Icons.person,
                  title: 'My Profile',
                  subtitle: 'Manage your account',
                  onTap: () {
                    Navigator.pop(context);
                    // Navigate to profile screen
                  },
                ),

                _buildDrawerItem(
                  icon: Icons.local_offer_outlined,
                  selectedIcon: Icons.local_offer,
                  title: 'Offers & Discounts',
                  subtitle: 'Save on your orders',
                  badge: '3 New',
                  onTap: () {
                    Navigator.pop(context);
                    // Navigate to offers screen
                  },
                ),

                // Support section
                const SizedBox(height: 16),
                _buildSectionHeader('Support'),

                _buildDrawerItem(
                  icon: Icons.help_outline,
                  selectedIcon: Icons.help,
                  title: 'Help & Support',
                  subtitle: '24/7 customer service',
                  onTap: () {
                    Navigator.pop(context);
                    // Navigate to help screen
                  },
                ),

                _buildDrawerItem(
                  icon: Icons.info_outline,
                  selectedIcon: Icons.info,
                  title: 'About Us',
                  subtitle: 'Learn more about our app',
                  onTap: () {
                    Navigator.pop(context);
                    // Navigate to about screen
                  },
                ),
              ],
            ),
          ),

          // Bottom section with logout
          Container(
            decoration: const BoxDecoration(
              border: Border(
                top: BorderSide(
                  color: AppColors.borderLight,
                  width: 1,
                ),
              ),
            ),
            child: _buildDrawerItem(
              icon: Icons.logout,
              title: 'Logout',
              subtitle: 'Sign out of your account',
              isDestructive: true,
              onTap: () {
                Navigator.pop(context);
                _showLogoutDialog(context);
              },
            ),
          ),
        ],
      ),
    );
  }

  /// Builds the drawer header with user information and beautiful gradient background
  ///
  /// Features:
  /// - Gradient background using primary and secondary colors
  /// - User avatar with placeholder for profile image
  /// - User name and email with proper typography
  /// - Modern spacing and layout
  Widget _buildDrawerHeader() {
    return Container(
      height: 200,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.primary, AppColors.secondary],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // User avatar with border and shadow
              Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: AppColors.textOnPrimary.withOpacity(0.3),
                    width: 2,
                  ),
                  boxShadow: const [
                    BoxShadow(
                      color: AppColors.shadowMedium,
                      blurRadius: 8,
                      offset: Offset(0, 4),
                    ),
                  ],
                ),
                child: const CircleAvatar(
                  radius: 32,
                  backgroundColor: AppColors.accentLight,
                  child: Icon(
                    Icons.person,
                    size: 32,
                    color: AppColors.primary,
                  ),
                  // TODO: Replace with actual user image
                  // backgroundImage: NetworkImage(userImageUrl),
                ),
              ),

              const SizedBox(height: 16),

              // User name
              const Text(
                'Jesika Sabrina',
                style: TextStyle(
                  color: AppColors.textOnPrimary,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  letterSpacing: 0.5,
                ),
              ),

              const SizedBox(height: 4),

              // User email
              Text(
                '<EMAIL>',
                style: TextStyle(
                  color: AppColors.textOnPrimary.withOpacity(0.9),
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                ),
              ),

              const SizedBox(height: 8),

              // Status indicator or additional info
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: AppColors.textOnPrimary.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Text(
                  'Premium Member',
                  style: TextStyle(
                    color: AppColors.textOnPrimary,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds a section header for grouping drawer items
  ///
  /// [title] - The section title to display
  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(20, 16, 20, 8),
      child: Text(
        title,
        style: const TextStyle(
          color: AppColors.textSecondary,
          fontSize: 12,
          fontWeight: FontWeight.w600,
          letterSpacing: 1.2,
        ),
      ),
    );
  }

  /// Builds a modern drawer item with enhanced styling and interaction
  ///
  /// Parameters:
  /// - [icon] - The main icon to display
  /// - [selectedIcon] - Icon to show when item is selected (optional)
  /// - [title] - The main title text
  /// - [subtitle] - Optional subtitle text for additional context
  /// - [badge] - Optional badge text (e.g., "New", "3")
  /// - [isSelected] - Whether this item is currently selected
  /// - [isDestructive] - Whether this is a destructive action (like logout)
  /// - [onTap] - Callback when item is tapped
  Widget _buildDrawerItem({
    required IconData icon,
    IconData? selectedIcon,
    required String title,
    String? subtitle,
    String? badge,
    bool isSelected = false,
    bool isDestructive = false,
    required VoidCallback onTap,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 2),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: isSelected ? AppColors.primary.withOpacity(0.1) : null,
      ),
      child: ListTile(
        // Icon with proper theming
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: isSelected
                ? AppColors.primary
                : isDestructive
                    ? AppColors.error.withOpacity(0.1)
                    : AppColors.surfaceVariant,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            isSelected && selectedIcon != null ? selectedIcon : icon,
            color: isSelected
                ? AppColors.textOnPrimary
                : isDestructive
                    ? AppColors.error
                    : AppColors.textSecondary,
            size: 20,
          ),
        ),

        // Title and subtitle
        title: Text(
          title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
            color: isDestructive ? AppColors.error : AppColors.textPrimary,
          ),
        ),

        subtitle: subtitle != null
            ? Text(
                subtitle,
                style: TextStyle(
                  fontSize: 12,
                  color: isDestructive
                      ? AppColors.error.withOpacity(0.7)
                      : AppColors.textSecondary,
                ),
              )
            : null,

        // Badge or trailing icon
        trailing: badge != null
            ? Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: AppColors.primary,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  badge,
                  style: const TextStyle(
                    color: AppColors.textOnPrimary,
                    fontSize: 10,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              )
            : isSelected
                ? const Icon(
                    Icons.check_circle,
                    color: AppColors.primary,
                    size: 20,
                  )
                : null,

        onTap: onTap,
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),

        // Hover effect for better interaction feedback
        hoverColor: AppColors.hover,
        splashColor: AppColors.pressed,
      ),
    );
  }

  /// Shows a modern logout confirmation dialog with proper theming
  ///
  /// Features:
  /// - Custom styling matching app theme
  /// - Clear action buttons with proper colors
  /// - Icon for visual context
  /// - Proper spacing and typography
  void _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: AppColors.surface,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),

          // Icon and title
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.error.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.logout,
                  color: AppColors.error,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'Logout',
                style: TextStyle(
                  color: AppColors.textPrimary,
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),

          // Content message
          content: const Text(
            'Are you sure you want to logout? You will need to sign in again to access your account.',
            style: TextStyle(
              color: AppColors.textSecondary,
              fontSize: 16,
              height: 1.4,
            ),
          ),

          // Action buttons
          actions: [
            // Cancel button
            TextButton(
              onPressed: () => Navigator.pop(context),
              style: TextButton.styleFrom(
                foregroundColor: AppColors.textSecondary,
                padding:
                    const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              ),
              child: const Text(
                'Cancel',
                style: TextStyle(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),

            // Logout button
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                // TODO: Implement logout logic here
                // Example: AuthService.logout();
                // Navigate to login screen
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.error,
                foregroundColor: AppColors.textOnPrimary,
                padding:
                    const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                'Logout',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],

          actionsPadding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
          contentPadding: const EdgeInsets.fromLTRB(24, 16, 24, 0),
          titlePadding: const EdgeInsets.fromLTRB(24, 24, 24, 0),
        );
      },
    );
  }
}
