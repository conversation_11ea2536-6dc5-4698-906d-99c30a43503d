import 'package:flutter/material.dart';

import '../theme/app_colors.dart';

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
          preferredSize: const Size.fromHeight(50),
          child: AppBar(
            title: const Text('Online Medicine'),
            actions: [
              // notification icon
              IconButton(
                onPressed: () {},
                icon: const Icon(Icons.notifications),
              ),
              // profile icon
              IconButton(
                onPressed: () {},
                icon: const Icon(Icons.person),
              ),
            ],
          )),
      // Add the drawer here
      drawer: _buildDrawer(context),
      body: const Center(
        child: Text('Main Content Area'),
      ),
    );
  }

  Widget _buildDrawer(BuildContext context) {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero, // Remove default padding
        children: [
          // Header section with user info
          _buildDrawerHeader(),

          // Menu items
          _buildDrawerItem(
            icon: Icons.upload_file,
            title: 'Upload Prescription',
            onTap: () {
              Navigator.pop(context); // Close drawer
              // Navigate to upload prescription screen
            },
          ),

          _buildDrawerItem(
            icon: Icons.shopping_cart,
            title: 'Supplements',
            onTap: () {
              Navigator.pop(context);
              // Navigate to supplements screen
            },
          ),

          _buildDrawerItem(
            icon: Icons.person,
            title: 'My Profile',
            onTap: () {
              Navigator.pop(context);
              // Navigate to profile screen
            },
          ),

          _buildDrawerItem(
            icon: Icons.local_offer,
            title: 'Offer & Discounts',
            onTap: () {
              Navigator.pop(context);
              // Navigate to offers screen
            },
          ),

          _buildDrawerItem(
            icon: Icons.help_outline,
            title: 'Help & Support',
            onTap: () {
              Navigator.pop(context);
              // Navigate to help screen
            },
          ),

          const Divider(), // Visual separator

          _buildDrawerItem(
            icon: Icons.logout,
            title: 'Logout',
            onTap: () {
              Navigator.pop(context);
              _showLogoutDialog(context);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildDrawerHeader() {
    return const DrawerHeader(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.primary, AppColors.secondary],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CircleAvatar(
            radius: 30,
            // backgroundImage: NetworkImage(
            //   'https://via.placeholder.com/150', // Replace with actual image
            // ),
          ),
          SizedBox(height: 10),
          Text(
            'Jesika Sabrina',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            '<EMAIL>',
            style: TextStyle(
              color: Colors.white70,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDrawerItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: Colors.grey[700],
      ),
      title: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
      ),
      onTap: onTap,
      contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 5),
    );
  }

  void _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Logout'),
          content: const Text('Are you sure you want to logout?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                // Implement logout logic here
              },
              child: const Text('Logout'),
            ),
          ],
        );
      },
    );
  }
}
